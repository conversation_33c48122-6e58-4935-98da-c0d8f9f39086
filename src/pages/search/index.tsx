import React, { useEffect, useState } from 'react'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import Link from 'next/link'
import { Search } from '@/components'
import * as api from '@/api'
import { ISearchResData } from '@/api'

const HighlightText = ({
  text,
  keyword,
}: {
  text: string
  keyword: string
}) => {
  if (!keyword) return <span>{text}</span> // 如果没有关键词，直接返回原文本

  // 创建正则表达式，使用 'gi' 标志进行全局和不区分大小写的匹配
  const regex = new RegExp(`(${keyword})`, 'gi')

  // 使用 split 方法将文本分割为数组
  const parts = text.split(regex)

  return (
    <div>
      {parts.map((part, index) =>
        // 如果部分与关键词匹配，则包裹在 <span> 中
        part.toLowerCase() === keyword.toLowerCase() ? (
          <span key={index} className="text-[#C00000]">
            {part}
          </span>
        ) : (
          part
        ),
      )}
    </div>
  )
}

const Loading = () => {
  return (
    <div className="absolute inset-x-0 top-[60px] flex items-center justify-center">
      <svg className="size-10 animate-spin" viewBox="25 25 50 50">
        <circle
          className={`animate-[dash_1.5s_ease-in-out_infinite] stroke-[#19191a]`}
          cx="50"
          cy="50"
          r="20"
          fill="none"
          strokeWidth="3"
          strokeMiterlimit="10"
          strokeDasharray="1,200"
          strokeDashoffset="0"
          strokeLinecap="round"
        />
      </svg>
    </div>
  )
}

function SearchResult() {
  const router = useRouter()
  const { keyword = '' } = router.query as { keyword: string }

  const [loading, setLoading] = useState<boolean>(true)
  const [datas, setDatas] = useState<ISearchResData[]>([])

  useEffect(() => {
    setLoading(true)
    api
      .doSearch(keyword)
      .then((res) => {
        setDatas(res)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [keyword])

  if (loading) {
    return (
      <>
        <Loading />
      </>
    )
  }

  return (
    <>
      <div
        className={clsx(
          'absolute bottom-0 left-0 right-0 top-0 z-0 bg-[#FAFAFB]',
          {
            hidden: datas.length,
          },
        )}
      ></div>
      <div
        className={clsx(
          'relative mx-auto h-full w-full max-w-[1200px] px-6 py-[60px] text-textColorBlack',
        )}
      >
        {datas.length ? (
          <div className="relative">
            <h3 className="mb-5 text-[24px]/[32px] font-semibold">
              Search result for ``{keyword}``{' '}
              {datas.length && `(${datas.length})`}
            </h3>

            <ul className="mb-5">
              {datas.map((item, index) => {
                return (
                  <li className="mb-4" key={index}>
                    <Link href={`/docs/${item.id}`}>
                      <div className="mb-3 text-[16px]/[24px] font-semibold hover:underline">
                        <HighlightText text={item.summary} keyword={keyword} />
                      </div>
                      <div className="mt-3 text-sm/[22px] text-textColorGray hover:text-[#19191A]">
                        <HighlightText text={item.desc} keyword={keyword} />
                      </div>
                    </Link>
                  </li>
                )
              })}
            </ul>

            {/* <Pagination
              current={1}
              total={100}
              simple={true}
              align="center"
              onChange={handlePageChange}
            /> */}
          </div>
        ) : (
          <div className="relative mx-auto max-w-[680px] rounded-lg bg-white p-8 lg:top-28">
            {keyword && (
              <>
                <h3 className="mb-4 text-[20px]/[28px] font-semibold">
                  Search result for ``{keyword}``
                </h3>
              </>
            )}
            <p className="mb-2 text-[16px]/[24px] font-semibold">
              Sorry, no results were found.
            </p>
            <p className="mb-4 text-[14px]/[22px] text-textColorGray">
              Please try again with different keywords.
            </p>
            <div className="w-full bg-[#FAFAFB]">
              <Search />
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default SearchResult
