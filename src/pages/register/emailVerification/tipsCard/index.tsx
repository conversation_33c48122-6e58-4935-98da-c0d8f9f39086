import { useRouter } from 'next/router'
import { useMemo } from 'react'

export default function TipsCard() {
  const router = useRouter()
  const { from, params } = router.query
  const customerData = useMemo(() => {
    if (params) {
      return JSON.parse(atob(params as string))
    }
    return {
      customers_firstname: 'xxxx',
      customers_email_address: '<EMAIL>',
    }
  }, [params])
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="flex w-[680px] flex-col items-center rounded-lg bg-white p-[32px] shadow-lg">
        {/* 成功提示部分 */}
        <div className="text-center">
          <div className="mb-4 flex items-center justify-center gap-2">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
              <svg
                className="h-4 w-4 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <span className="text-[20px] font-semibold text-[#19191A]">
              Hi, {customerData.customers_firstname} (
              {customerData.customers_email_address})
            </span>
          </div>
        </div>

        {/* 说明文字部分 */}
        <div className="text-center font-sans text-[14px] leading-[22px] text-[#707070]">
          <p className="mb-2">
            {/* 加入企业提示 */}
            {from === 'joinCompany' && (
              <>
                The application has been submitted. Please wait for the
                administrator&apos;s confirmation. During this time, you can
                still access the website normally. Contact the account manager
                if you need help.
              </>
            )}
            {/* 保持普通账户提示 */}
            {from === 'keepPersonalAccount' && (
              <>
                You have keep the Individual Account. If you would like to join
                the business account, you can always contact your business
                account administrator or contact your account manager.
              </>
            )}
          </p>
        </div>
      </div>
    </div>
  )
}
