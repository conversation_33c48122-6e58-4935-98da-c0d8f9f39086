import FsButton from '@/components/ui/FsButton'
import message from '@/components/ui/Message'
import userApi from '@/services/api/user'
import { useRouter } from 'next/router'
import { useEffect, useRef, useState } from 'react'

export default function JoinBusinessAccount() {
  const router = useRouter()
  const { id, from } = router.query
  const [isLoading, setIsLoading] = useState(false)
  const [companyInfo, setCompanyInfo] = useState({
    companyName: '',
    customerEmail: '',
  })
  const bizAccountId = useRef<string | null>(null)
  const customerData = useRef<any>(null)

  useEffect(() => {
    const fetchJoinCompanyInfo = async () => {
      setIsLoading(true)
      console.log('fetchJoinCompanyInfo')
      try {
        const fn = () =>
          id
            ? userApi.getJoinCompanyInfoByCid({ cid: id as string })
            : userApi.getJoinCompanyInfo()

        const res = (await fn()) as any
        console.log(res)
        bizAccountId.current = res.data.bizAccountId
        customerData.current = res.data.customerData
        setCompanyInfo({
          companyName: res.data.companyName,
          customerEmail: res.data.customerData.customers_email_address,
        })
      } catch (error: any) {
        message.error('Failed to get join company info: ' + error.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchJoinCompanyInfo()
  }, [id])

  const [joinLoading, setJoinLoading] = useState(false)
  const handleJoin = async () => {
    setJoinLoading(true)
    try {
      const url = id
        ? `/api/companyAccount/applyJoinCompany`
        : '/api/companyAccount/applyJoinCompanyForCurrent'
      const res = await userApi.joinCompany(url, {
        cid: id as string,
        bizAccountId: bizAccountId.current,
      })
      if (res.code === 200) {
        router.push({
          pathname: '/register/emailVerification/tipsCard',
          query: {
            from: 'joinCompany',
            params: btoa(JSON.stringify(customerData.current)),
          },
        })
      }
    } catch (error: any) {
      message.error('Failed to join company: ' + error.message)
    } finally {
      setJoinLoading(false)
    }
  }
  const handleKeepPersonalAccount = async () => {
    setJoinLoading(true)
    try {
      const url = id
        ? `/api/companyAccount/applyNotJoinCompany`
        : '/api/companyAccount/applyNotJoinCompanyForCurrent'
      const res = await userApi.keepPersonalAccount(url, {
        cid: id as string,
        bizAccountId: bizAccountId.current,
      })
      if (res.code === 200) {
        router.push({
          pathname: '/register/emailVerification/tipsCard',
          query: {
            from: 'keepPersonalAccount',
            params: btoa(JSON.stringify(customerData.current)),
          },
        })
      }
    } catch (error: any) {
      message.error('Failed to keep personal account: ' + error.message)
    } finally {
      setJoinLoading(false)
    }
  }
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/25 backdrop-blur-sm">
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="h-16 w-16 animate-spin rounded-full border-4 border-gray-200 border-t-blue-500 shadow-lg" />
            </div>
          </div>
        </div>
      )}
      <div className="flex w-[680px] flex-col rounded-lg bg-white p-[32px] shadow-lg">
        {/* 标题部分 */}
        <h1 className="mb-4 text-[20px] font-semibold text-[#19191A]">
          Join FS Business Account
        </h1>

        {/* 说明文字部分 */}
        <div className="mb-6 font-sans text-[14px] leading-[22px] text-[#707070]">
          <p className="mb-2">
            We have found a business account that matches your email address:
            <span className="font-semibold text-[#0066FF]">
              {companyInfo.companyName}
            </span>
            . And FS API service is currently available exclusively to FS
            Business Account users.
          </p>
          <p className="mb-2">
            Before we continue, please review what this means and confirm that
            you are authorized to do so:
          </p>
          <ul className="list-disc pl-5">
            <li className="mb-2">
              Your application needs to be approved by the administrator before
              it can be successfully joined, and until then you can only access{' '}
              <a
                href="https://fs.com"
                className="text-[#0066FF] hover:text-blue-700"
              >
                FS.com
              </a>{' '}
              as a personal account.
            </li>
            <li>
              When your account (
              <a
                href="mailto:<EMAIL>"
                className="text-[#0066FF] hover:text-blue-700"
              >
                {companyInfo.customerEmail}
              </a>
              ) becomes a business account, your purchases are shared with your
              teams and you can also access the shared information of your team
              members (saved carts, quotes, orders, invoices, and returns). And
              your historical data will only be visible to yourself.
            </li>
          </ul>
        </div>

        {/* 按钮部分 */}
        <div className="flex items-center justify-end">
          <button
            className="mr-[16px] rounded-md bg-[transparent] px-6 py-2.5 text-[#707070] transition-colors hover:bg-[#F2F2F2]"
            onClick={handleKeepPersonalAccount}
          >
            Keep the Personal Account
          </button>
          <FsButton
            loading={joinLoading}
            className="rounded-md bg-red-600 px-6 py-2.5 text-white transition-colors hover:bg-red-700"
            onClick={handleJoin}
          >
            Join the FS Business Account
          </FsButton>
        </div>
      </div>
    </div>
  )
}
