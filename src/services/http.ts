import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { createHmac } from 'crypto'
import Cookies from 'js-cookie'

// Base64 编码函数
const Base64 = {
  encode: (str: string): string => {
    return Buffer.from(str).toString('base64')
  },
}

// 生成随机字符串
function genNonce(num: number): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let str = ''
  for (let i = 0; i < num; i++) {
    const pos = Math.round(Math.random() * (chars.length - 1))
    str += chars[pos]
  }
  return str
}
// 创建 axios 实例
const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api', // 设置基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 生成请求头所需的参数
    const timestamps = new Date().getTime()
    const nonce = genNonce(16)
    const apiKey = 'yuxuanxuanpc'
    const payload = '[]'

    // 生成签名
    const s = timestamps + nonce + payload
    const hmac = createHmac('sha256', 'yuxuan3507')
    hmac.update(s)
    const signature = Base64.encode(hmac.digest('hex'))
    // 设置websiteInfo
    const websiteInfo = {
      language: 'en',
      iso_code: 'US',
      currency: 'USD',
    }
    // 设置请求头
    config.headers.timestamps = timestamps
    config.headers.nonce = nonce
    config.headers.apiKey = apiKey
    config.headers.clientSignature = signature
    config.headers.websiteInfo = Base64.encode(JSON.stringify(websiteInfo))
    // 添加 token（保留原有的 token 逻辑）
    const token = Cookies.get('token_new')
    // const token = 'Bearer ' + window.localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 这里可以统一处理响应数据
    return response.data
  },
  (error) => {
    // 这里可以统一处理错误
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，可以跳转到登录页
          window.location.href = `${process.env.NEXT_PUBLIC_LOGIN_URL}/login.html?redirect=${window.location.href}&source=api`
          break
        case 403:
          // 禁止访问
          break
        case 404:
          // 资源不存在
          break
        case 500:
          // 服务器错误
          break
        default:
          console.error('请求错误:', error)
      }
    }
    return Promise.reject(error.response.data)
  },
)

// 封装请求方法
export const http = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => {
    return instance.get<any, T>(url, config)
  },

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return instance.post<any, T>(url, data, config)
  },

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => {
    return instance.put<any, T>(url, data, config)
  },

  delete: <T = any>(url: string, config?: AxiosRequestConfig) => {
    return instance.delete<any, T>(url, config)
  },
}

export default http
