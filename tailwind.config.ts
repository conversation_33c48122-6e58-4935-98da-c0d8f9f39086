import type { Config } from 'tailwindcss'
import typography from '@tailwindcss/typography'

export default {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        textColorBlack: 'var(--textColorBlack)',
        textColorGray: 'var(--textColorGray)',
        textColorLink: 'var(--textColorLink)',
        tableHeaderPrimary: 'var(--tableHeaderPrimary)',
        tableHeaderBorderPrimary: 'var(--tableHeaderBorderPrimary)',
        hoverBgRgba: 'rgba(25, 25, 26, 0.04);',
        bgRgba1: 'rgba(64, 128, 255, 0.06)',
        bgRgba2: 'rgba(192, 0, 0, 0.04)',
      },
      boxShadow: {
        'tips-shadow': '0 1px 8px 0 rgba(120, 102, 102, 0.3)',
      },
      borderColor: {
        'table-header-rgba': 'rgba(64, 128, 255, 0.18)',
      },
      typography: {
        DEFAULT: {
          css: {
            fontSize: '14px',
            lineHeight: '22px',
            color: 'var(--textColorBlack)',
            a: {
              textDecoration: 'none',
              color: 'var(--textColorBlack)',
            },
            h3: {
              fontSize: '20px',
              lineHeight: '28px',
              marginTop: '24px',
              marginBottom: '16px',
            },
            h4: {
              fontSize: '16px',
              lineHeight: '24px',
              marginBottom: '16px',
            },
            'h3 a, h4 a': {
              fontWeight: 600,
            },
          },
        },
        light: {
          css: {
            color: 'var(--textColorGray)',
          },
        },
      },
      keyframes: {
        dash: {
          '0%': { strokeDasharray: '1, 200', strokeDashoffset: '0' },
          '50%': { strokeDasharray: '89, 200', strokeDashoffset: '-35px' },
          '100%': { strokeDasharray: '89, 200', strokeDashoffset: '-124px' },
        },
        fadeIn: {
          '0%': {
            opacity: '0',
          },
          '100': {
            opacity: '1',
          },
        },
        fadeOut: {
          '0': {
            opacity: '1',
          },
          '100': {
            opacity: '0',
          },
        },
      },
      animation: {
        dash: 'dash 1.5s ease-in-out infinite',
        fadeIn: 'fadeIn 0.3s ease-in-out 0s 1 normal forwards;',
        fadeOut: 'fadeOut 0.3s ease-in-out 0s 1 normal forwards;',
      },
    },
  },
  plugins: [typography],
} satisfies Config
